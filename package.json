{"name": "ai-reading-extension", "private": true, "version": "1.8.0", "type": "module", "scripts": {"dev": "vite", "build:content": "vite build -c vite.content.config.ts", "build:background": "vite build -c vite.background.config.ts", "build:popup": "vite build -c vite.popup.config.ts", "cleanup": "node scripts/cleanup.js", "build": "pnpm run build:content && pnpm run build:background && pnpm run build:popup && cp -r public/* dist/ && pnpm run cleanup", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "watch": "vite build --watch"}, "dependencies": {"@headlessui/react": "^2.2.0", "@mozilla/readability": "^0.5.0", "@types/chrome": "^0.0.287", "@types/node": "^22.10.2", "autoprefixer": "^10.4.20", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "postcss": "^8.4.49", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.1.0", "tailwindcss": "^3.4.17", "turndown": "^7.2.0", "zustand": "^4.5.2"}, "devDependencies": {"@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@types/turndown": "^5.0.5", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "terser": "^5.43.1", "typescript": "^5.2.2", "vite": "^5.1.6"}}