# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.zip

# Added by Task Master AI
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
.vscode
# OS specific

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

## 项目自带的无用配置文件
.roomodes
.windsurfrules
*.zip
.roo/
.taskmaster/
.cursor/
.clinerules
.cursor/
.windsurf/
.trae/
.github/

.gemini/
.cursor/

AGENTS.md
CLAUDE.md

# Task files
# tasks.json
# tasks/ 