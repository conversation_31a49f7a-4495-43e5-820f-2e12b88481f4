<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>内容提取器测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #333;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
    }
    code {
      font-family: Consolas, monospace;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    img {
      max-width: 100%;
      height: auto;
    }
    blockquote {
      border-left: 3px solid #ccc;
      margin: 20px 0;
      padding-left: 20px;
      color: #666;
    }
  </style>
</head>
<body>
  <header>
    <h1>内容提取器测试页面</h1>
    <p>这个页面用于测试内容提取器的各种功能</p>
  </header>

  <nav>
    <ul>
      <li><a href="#section1">第一部分</a></li>
      <li><a href="#section2">第二部分</a></li>
      <li><a href="#section3">第三部分</a></li>
    </ul>
  </nav>

  <main>
    <section id="section1">
      <h2>1. 表格测试</h2>
      <p>下面是一个标准表格，用于测试表格提取器：</p>
      
      <table>
        <thead>
          <tr>
            <th>名称</th>
            <th>类型</th>
            <th>描述</th>
            <th>默认值</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>fontSize</td>
            <td>number</td>
            <td>字体大小（像素）</td>
            <td>16</td>
          </tr>
          <tr>
            <td>lineHeight</td>
            <td>number</td>
            <td>行高</td>
            <td>1.5</td>
          </tr>
          <tr>
            <td>theme</td>
            <td>string</td>
            <td>主题（light/dark）</td>
            <td>light</td>
          </tr>
          <tr>
            <td>fontFamily</td>
            <td>string</td>
            <td>字体系列</td>
            <td>default</td>
          </tr>
        </tbody>
      </table>

      <p>下面是一个没有表头的表格：</p>
      
      <table>
        <tr>
          <td>苹果</td>
          <td>红色</td>
          <td>甜</td>
        </tr>
        <tr>
          <td>香蕉</td>
          <td>黄色</td>
          <td>甜</td>
        </tr>
        <tr>
          <td>柠檬</td>
          <td>黄色</td>
          <td>酸</td>
        </tr>
      </table>

      <p>下面是一个结构不完整的表格：</p>
      
      <table>
        <tr>
          <td><strong>产品</strong></td>
          <td><strong>价格</strong></td>
          <td><strong>库存</strong></td>
        </tr>
        <tr>
          <td>笔记本电脑</td>
          <td>5999</td>
          <td>15</td>
        </tr>
        <tr>
          <td>手机</td>
          <td>3999</td>
          <td>42</td>
        </tr>
      </table>
    </section>

    <section id="section2">
      <h2>2. 图片和媒体测试</h2>
      <p>下面是一些图片测试：</p>

      <figure>
        <img src="https://picsum.photos/800/400" alt="随机图片示例 1">
        <figcaption>带有标题的图片示例</figcaption>
      </figure>

      <p>下面是一个没有 figure 包装的图片：</p>
      <img src="https://picsum.photos/800/401" alt="随机图片示例 2">

      <p>下面是一个带有懒加载属性的图片：</p>
      <img data-src="https://picsum.photos/800/402" alt="懒加载图片示例" loading="lazy">

      <p>下面是一个视频示例：</p>
      <video controls width="100%">
        <source src="https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.mp4" type="video/mp4">
        您的浏览器不支持视频标签。
      </video>

      <p>下面是一个 iframe 示例：</p>
      <iframe width="100%" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
    </section>

    <section id="section3">
      <h2>3. 代码块和列表测试</h2>
      
      <h3>3.1 代码块测试</h3>
      <p>下面是一个 JavaScript 代码块：</p>
      
      <pre><code class="language-javascript">
// 这是一个简单的 JavaScript 函数
function greet(name) {
  return `Hello, ${name}!`;
}

// 调用函数
const message = greet('World');
console.log(message);  // 输出: Hello, World!
      </code></pre>

      <p>下面是一个没有指定语言的代码块：</p>
      
      <pre><code>
# 这是一个 Python 代码示例
def factorial(n):
    if n == 0 or n == 1:
        return 1
    else:
        return n * factorial(n-1)

# 计算 5 的阶乘
result = factorial(5)
print(f"5! = {result}")  # 输出: 5! = 120
      </code></pre>

      <p>下面是一个没有 code 标签的代码块：</p>
      
      <pre>
SELECT name, age, email
FROM users
WHERE age > 18
ORDER BY name ASC;
      </pre>

      <h3>3.2 列表测试</h3>
      
      <p>下面是一个有序列表：</p>
      
      <ol>
        <li>第一项</li>
        <li>第二项
          <ol>
            <li>子项 2.1</li>
            <li>子项 2.2</li>
          </ol>
        </li>
        <li>第三项</li>
      </ol>

      <p>下面是一个无序列表：</p>
      
      <ul>
        <li>苹果</li>
        <li>香蕉
          <ul>
            <li>小香蕉</li>
            <li>大香蕉</li>
          </ul>
        </li>
        <li>橙子</li>
      </ul>

      <p>下面是一个结构不完整的列表：</p>
      
      <ul>
        <li>第一项</li>
        第二项（没有 li 标签）
        <li>第三项</li>
        <ol>
          <li>这个嵌套列表没有在 li 中</li>
        </ol>
      </ul>

      <p>下面是一个带有段落的列表：</p>
      
      <ol>
        <li>
          <p>这是第一项，包含一个段落。</p>
          <p>这是第一项的第二个段落。</p>
        </li>
        <li>
          <p>这是第二项，也包含一个段落。</p>
        </li>
      </ol>
    </section>

    <section>
      <h2>4. 引用和其他元素测试</h2>
      
      <h3>4.1 引用测试</h3>
      
      <blockquote>
        <p>这是一个引用块。引用块通常用于引用他人的话或文章片段。</p>
        <p>这是引用块的第二段。</p>
        <cite>— 某人</cite>
      </blockquote>

      <h3>4.2 混合内容测试</h3>
      
      <p>这是一段包含<code>内联代码</code>的文本，还有<strong>加粗文本</strong>和<em>斜体文本</em>。</p>
      
      <p>这是一段中英文混合的文本。This is a paragraph with mixed Chinese and English text. 中文和英文之间应该有适当的间距。</p>
      
      <p>这是一段包含链接的文本：<a href="https://example.com">示例链接</a>。</p>
    </section>
  </main>

  <aside>
    <h3>侧边栏</h3>
    <p>这是一个侧边栏，通常包含一些辅助信息。</p>
    <ul>
      <li><a href="#">链接 1</a></li>
      <li><a href="#">链接 2</a></li>
      <li><a href="#">链接 3</a></li>
    </ul>
  </aside>

  <footer>
    <p>&copy; 2023 测试页面。保留所有权利。</p>
  </footer>
</body>
</html>
