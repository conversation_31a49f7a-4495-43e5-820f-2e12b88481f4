/* 阅读模式的基础样式 - 基于 Obsidian Clipper 设计理念 */
#reading-mode-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  overflow-y: auto;
  background-color: var(--reading-bg-color);
  color: var(--reading-text-color);
  font-family: var(--reading-font-family);
  line-height: var(--reading-line-height);
  letter-spacing: var(--reading-letter-spacing);

  /* 更加灵活的内边距 */
  padding: max(4rem, 4vh) max(2rem, 5vw);

  /* 平滑过渡效果 */
  transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);

  /* 添加进入动画 */
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标题系统优化 */
#reading-mode-container h1,
#reading-mode-container h2,
#reading-mode-container h3,
#reading-mode-container h4,
#reading-mode-container h5,
#reading-mode-container h6 {
  @apply mt-12 mb-4 leading-tight font-bold text-gray-900 tracking-tight relative;
  font-family: 'Georgia', 'Times New Roman', serif;
}

#reading-mode-container h1 {
  @apply text-5xl pb-6 mb-10 text-center relative;
  background: linear-gradient(to right, transparent, var(--reading-accent-color, #3b82f6) 20%, var(--reading-accent-color, #3b82f6) 80%, transparent);
  background-size: 100% 1px;
  background-position: bottom;
  background-repeat: no-repeat;
  padding-bottom: 1.5rem;

  /* 添加微妙的文字阴影 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  /* 添加动画 */
  animation: slideInDown 0.8s ease forwards;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#reading-mode-container h2 {
  @apply text-3xl pl-4 border-l-4 border-brand-500 ml-[-1.25em] mb-8 mt-12;
  position: relative;
  transition: all 0.3s ease;
}

#reading-mode-container h2:hover {
  @apply border-brand-600;
  transform: translateX(5px);
}

#reading-mode-container h3 {
  @apply text-2xl text-brand-700 mb-6 mt-10;
  position: relative;
  padding-bottom: 0.5rem;
}

#reading-mode-container h3::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 3rem;
  height: 2px;
  background-color: var(--reading-accent-color, #3b82f6);
  opacity: 0.5;
  transition: width 0.3s ease;
}

#reading-mode-container h3:hover::after {
  width: 5rem;
}

#reading-mode-container h4 {
  @apply text-xl text-gray-800 mb-5 mt-8;
  font-weight: 600;
}

/* 段落和内容块优化 */
#reading-mode-container p {
  @apply my-7 leading-relaxed text-gray-700 text-justify hyphens-auto;
  text-indent: 2em;
  line-height: 1.8;
  letter-spacing: 0.01em;
}

#reading-mode-container img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1.5em auto;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

#reading-mode-container img:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

/* 引用块优化 */
#reading-mode-container blockquote {
  margin: 1.5em 0;
  padding: 1em 1.5em;
  border-left: 3px solid var(--reading-blockquote-border);
  background-color: var(--reading-blockquote-bg);
  border-radius: 4px;
  color: var(--reading-blockquote-text);
  font-style: italic;
  font-size: 1em;
  line-height: 1.6;
  position: relative;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* 列表优化 */
#reading-mode-container ul,
#reading-mode-container ol {
  @apply my-6 pl-6 text-gray-700 space-y-2 list-outside;
  line-height: 1.7;
}

#reading-mode-container li {
  @apply relative leading-relaxed mb-3;
}

/* 无序列表样式 */
#reading-mode-container ul {
  list-style: none;
}

#reading-mode-container ul>li {
  @apply pl-5;
}

#reading-mode-container ul>li::before {
  @apply absolute left-0 top-[0.6em] w-2 h-2 rounded-full bg-brand-500 transform scale-75;
  content: "";
}

/* 有序列表样式 */
#reading-mode-container ol {
  @apply pl-8;
  list-style: none;
  counter-reset: item;
}

#reading-mode-container ol>li {
  @apply pl-2;
  counter-increment: item;
}

#reading-mode-container ol>li::before {
  @apply absolute left-[-1.5em] top-0 text-brand-500 font-semibold text-sm;
  content: counter(item) ".";
}

/* 嵌套列表样式 */
#reading-mode-container ul ul,
#reading-mode-container ul ol,
#reading-mode-container ol ul,
#reading-mode-container ol ol {
  @apply mt-2 mb-0 ml-4;
}

#reading-mode-container ul ul>li::before {
  @apply bg-brand-400 scale-[0.6];
}

#reading-mode-container ol ol {
  counter-reset: subitem;
}

#reading-mode-container ol ol>li {
  counter-increment: subitem;
}

#reading-mode-container ol ol>li::before {
  content: counter(subitem, lower-alpha) ".";
  @apply text-brand-400;
}

/* 列表项内容样式 */
#reading-mode-container li>p {
  @apply my-1 !important;
  text-indent: 0 !important;
}

#reading-mode-container li>p:first-child {
  @apply mt-0 !important;
}

#reading-mode-container li>p:last-child {
  @apply mb-0 !important;
}

/* 深色主题列表样式 */
body.dark-theme #reading-mode-container ul,
body.dark-theme #reading-mode-container ol {
  @apply text-gray-300;
}

body.dark-theme #reading-mode-container ul>li::before {
  @apply bg-brand-400;
}

body.dark-theme #reading-mode-container ol>li::before {
  @apply text-brand-400;
}

/* 代码块容器优化 */
#reading-mode-container pre {
  @apply my-8 rounded-xl overflow-hidden relative shadow-card;
  background: #1e293b;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

/* 代码块顶部工具栏 */
#reading-mode-container pre::before {
  content: "";
  @apply absolute top-0 left-0 right-0 h-10;
  background: rgba(30, 41, 59, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 代码语言标签 */
#reading-mode-container pre::after {
  @apply absolute top-2.5 left-4 px-2 py-1 text-xs font-medium rounded-md z-20;
  content: attr(data-language);
  background: rgba(59, 130, 246, 0.1);
  color: #60a5fa;
  border: 1px solid rgba(59, 130, 246, 0.2);
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont;
}

/* 代码块复制按钮 */
#reading-mode-container pre .copy-button {
  @apply absolute top-2 right-4 px-3 py-1 rounded-md z-20 flex items-center gap-1.5 text-sm opacity-0 transition-opacity duration-200;
  background: #3b82f6;
  color: white;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont;
}

#reading-mode-container pre:hover .copy-button {
  @apply opacity-100;
}

#reading-mode-container pre .copy-button:hover {
  @apply bg-blue-700;
}

#reading-mode-container pre .copy-button.copied {
  @apply bg-green-500;
}

/* 代码内容 */
#reading-mode-container pre code {
  @apply block py-12 px-6 pt-16 overflow-x-auto text-gray-100 font-mono text-sm;
  line-height: 1.6;
  tab-size: 2;
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}

/* 代码行号 */
#reading-mode-container pre code.line-numbers {
  @apply pl-12 relative;
}

#reading-mode-container pre code.line-numbers>span {
  @apply relative;
}

#reading-mode-container pre code.line-numbers>span::before {
  @apply absolute -left-10 top-0 text-right w-8 text-gray-500 select-none;
  content: attr(data-line);
  font-size: 0.85em;
}

/* 分隔线 */
#reading-mode-container hr {
  @apply my-12 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent border-0;
}

/* 深色主题样式 */
body.dark-theme #reading-mode-container {
  @apply bg-gray-900 text-gray-200 border-gray-700;
  background-image: none;
}

body.dark-theme #reading-mode-container h1,
body.dark-theme #reading-mode-container h2,
body.dark-theme #reading-mode-container h3,
body.dark-theme #reading-mode-container h4,
body.dark-theme #reading-mode-container h5,
body.dark-theme #reading-mode-container h6 {
  @apply text-gray-100;
}

body.dark-theme #reading-mode-container h1::after {
  @apply bg-brand-400;
}

body.dark-theme #reading-mode-container h2 {
  @apply border-brand-400;
}

body.dark-theme #reading-mode-container h3 {
  @apply text-brand-400;
}

body.dark-theme #reading-mode-container p {
  @apply text-gray-300;
}

body.dark-theme #reading-mode-container blockquote {
  background-color: var(--reading-blockquote-bg);
  border-left-color: var(--reading-blockquote-border);
  color: var(--reading-blockquote-text);
}

body.dark-theme #reading-mode-container ul li::before {
  @apply bg-brand-400;
}

body.dark-theme #reading-mode-container pre {
  @apply bg-gray-900 border-gray-700;
}

body.dark-theme #reading-mode-container pre::before {
  @apply bg-gray-800 border-gray-700;
}

body.dark-theme #reading-mode-container pre::after {
  @apply bg-gray-800 text-brand-400 border-brand-500/20;
}

body.dark-theme #reading-mode-container pre code {
  @apply text-gray-300;
}

body.dark-theme #reading-mode-container pre code.line-numbers>span::before {
  @apply text-gray-600;
}

/* 滚动条样式 */
#reading-mode-container pre::-webkit-scrollbar {
  @apply h-2 bg-transparent;
}

#reading-mode-container pre::-webkit-scrollbar-track {
  @apply bg-transparent;
}

#reading-mode-container pre::-webkit-scrollbar-thumb {
  @apply bg-gray-600/50 rounded-full hover:bg-gray-500/70 transition-colors;
}

body.dark-theme #reading-mode-container pre::-webkit-scrollbar-thumb {
  @apply bg-gray-700/70;
}

body.dark-theme #reading-mode-container pre::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-600;
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  #reading-mode-container {
    @apply px-5 py-6;
    width: 100% !important;
    max-width: 100% !important;
  }

  #reading-mode-container h1 {
    @apply text-3xl pb-4 mb-6;
  }

  #reading-mode-container h2 {
    @apply text-2xl pl-3 ml-[-1em];
  }

  #reading-mode-container h3 {
    @apply text-xl;
  }

  #reading-mode-container h4 {
    @apply text-lg;
  }

  #reading-mode-container blockquote {
    @apply p-4 my-6;
  }

  #reading-mode-container pre {
    @apply rounded-lg my-6;
  }
}

/* 内容导航目录 */
#reading-mode-toc {
  @apply fixed top-32 right-8 w-56 bg-white/90 backdrop-blur-sm rounded-xl shadow-card p-4 border border-gray-200/30 max-h-[70vh] overflow-y-auto transition-all duration-300;
  opacity: 0.7;
  transform: translateX(0);
  display: none;
  /* 默认隐藏 */
}

/* 目录切换按钮 */
#toc-toggle-button {
  @apply fixed top-4 right-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full shadow-md flex items-center justify-center cursor-pointer z-50 transition-all duration-300;
  border: 1px solid rgba(0, 0, 0, 0.1);
  opacity: 0.7;
}

#toc-toggle-button:hover {
  @apply bg-white shadow-lg;
  opacity: 1;
}

body.dark-theme #toc-toggle-button {
  @apply bg-gray-800/90;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

body.dark-theme #toc-toggle-button:hover {
  @apply bg-gray-700;
}

#reading-mode-toc:hover {
  @apply opacity-100 shadow-float;
}

#reading-mode-toc.hidden {
  transform: translateX(calc(100% + 2rem));
}

#reading-mode-toc h3 {
  @apply text-base font-medium text-gray-800 mb-3 pb-2 border-b border-gray-100;
}

#reading-mode-toc ul {
  @apply list-none p-0 m-0 space-y-1;
}

#reading-mode-toc li {
  @apply text-sm;
}

#reading-mode-toc a {
  @apply block py-1.5 px-2 rounded-md text-gray-600 hover:bg-gray-100 hover:text-brand-600 transition-colors;
}

#reading-mode-toc a.active {
  @apply bg-brand-50 text-brand-600 font-medium;
}

#reading-mode-toc .toc-h1 {
  @apply ml-0;
}

#reading-mode-toc .toc-h2 {
  @apply ml-2;
}

#reading-mode-toc .toc-h3 {
  @apply ml-4;
}

#reading-mode-toc .toc-h4 {
  @apply ml-6;
}

/* 深色主题下的目录样式 */
body.dark-theme #reading-mode-toc {
  @apply bg-gray-800/90 border-gray-700/50;
}

body.dark-theme #reading-mode-toc h3 {
  @apply text-gray-200 border-gray-700;
}

body.dark-theme #reading-mode-toc a {
  @apply text-gray-400 hover:bg-gray-700/50 hover:text-brand-400;
}

body.dark-theme #reading-mode-toc a.active {
  @apply bg-brand-900/30 text-brand-400;
}

/* 快捷工具栏 */
#reading-mode-toolbar {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: var(--reading-content-bg-color);
  border: 1px solid var(--reading-border-color);
  border-radius: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  opacity: 0.7;
  transition: opacity 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
  z-index: 50;
}

#reading-mode-toolbar:hover {
  opacity: 1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#reading-mode-toolbar.hidden {
  transform: translateY(calc(100% + 2rem));
}

#reading-mode-toolbar button {
  background: transparent;
  border: none;
  color: var(--reading-muted-color);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

#reading-mode-toolbar button:hover {
  background-color: var(--reading-border-color);
  color: var(--reading-text-color);
}

#reading-mode-toolbar button.active {
  background-color: var(--reading-accent-color);
  color: white;
}

/* 深色主题下的工具栏样式由变量控制 */

.reading-mode-content {
  max-width: var(--reading-page-width);
  margin: 0 auto;
  /* 确保内容居中 */
  padding: var(--reading-content-padding);
  background-color: var(--reading-content-bg-color);
  border-radius: 12px;
  box-shadow: var(--reading-content-shadow);
  transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;

  /* 添加微妙的边框 */
  border: 1px solid rgba(0, 0, 0, 0.05);

  /* 添加进入动画，稍微延迟以产生层叠效果 */
  animation: contentFadeIn 0.6s ease 0.1s forwards;
  opacity: 0;
  transform: translateY(15px);

  /* 确保内容在容器中居中 */
  width: 100%;
  box-sizing: border-box;
}

@keyframes contentFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题下的边框 */
body.dark-theme .reading-mode-content {
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* 自定义滚动条样式 */
#reading-mode-container::-webkit-scrollbar {
  width: 8px;
}

#reading-mode-container::-webkit-scrollbar-track {
  background-color: var(--reading-scrollbar-track);
}

#reading-mode-container::-webkit-scrollbar-thumb {
  background-color: var(--reading-scrollbar-thumb);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

/* 退出动画 */
#reading-mode-container.exit-animation {
  animation: fadeOut 0.3s ease forwards;
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }

  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

#reading-mode-container::-webkit-scrollbar-thumb:hover {
  background-color: var(--reading-scrollbar-thumb-hover);
}

/* 文字选择样式 */
#reading-mode-container ::selection {
  background-color: var(--reading-selection-bg);
  color: var(--reading-selection-text);
}