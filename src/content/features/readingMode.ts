/**
 * 阅读模式功能模块
 * 负责处理阅读模式的切换、样式应用和交互
 */

import { StorageKeys, getStorage, FONT_FAMILIES, BACKGROUND_COLORS, CODE_THEMES } from '../../storage/storage';
import { DEFAULT_SETTINGS } from '../../constants/defaultSettings';
import { ReaderError, ErrorCode, ContentExtractionError } from '../../types/errors';
import { logger } from '../../utils/logManager';
import { TextSelectionToolbar } from '../components/TextSelectionToolbar';
import { MarkdownWorkerManager } from '../workers/markdownWorkerManager';
import { ExtractorFactory } from '../extractors/ExtractorFactory';
import { ContentExtractor } from '../extractors/contentExtractor';
import { ReadabilityExtractor } from '../extractors/ReadabilityExtractor';
import { DOMUtils } from '../../utils/dom';
import { RenderError } from '../../types/errors';

// Toast通知组件（简化版，实际项目中可能需要导入或实现）
const Toast = {
  info: (message: string, options?: any) => {
    console.log(`[INFO] ${message}`);
    return { close: () => { } };
  },
  success: (message: string, options?: any) => {
    console.log(`[SUCCESS] ${message}`);
  },
  error: (message: string, options?: any) => {
    console.log(`[ERROR] ${message}`);
  }
};

// 单例MarkdownWorkerManager
let markdownWorkerManager: MarkdownWorkerManager | null = null;

// 自定义渲染错误类型
class RenderError extends Error {
  constructor(message: string, public details?: any) {
    super(message);
    this.name = 'RenderError';
  }
}

// 阅读模式状态
let isReaderMode = false;
let originalScrollY = 0;
let originalContent: Node[] = [];
let extractedContent: string = '';

// 扩展ErrorCode类型
type ExtendedErrorCode = ErrorCode | 'RENDER_FAILED' | 'NETWORK_REQUEST_FAILED';

// 用户友好的错误消息
const userFriendlyMessages: Record<ExtendedErrorCode, string> = {
  CONTENT_EXTRACTION_FAILED: '无法提取页面内容，请尝试其他页面。',
  STORAGE_OPERATION_FAILED: '存储操作失败，您的设置可能未保存。',
  NETWORK_REQUEST_FAILED: '网络连接出现问题，请检查您的网络设置。',
  RENDER_FAILED: '显示内容时出现问题。',
  PERMISSION_DENIED: '缺少所需权限，请尝试重新启用插件。',
  TIMEOUT_EXCEEDED: '操作超时，请稍后重试。',
  VALIDATION_FAILED: '输入验证失败。',
  UNEXPECTED_STATE: '发生意外错误。',
};

// 阅读模式设置接口
interface ReadingModeSettings {
  theme: 'light' | 'dark';
  fontSize: number;
  codeFontSize: number;
  codeTheme: keyof typeof CODE_THEMES;
  lineHeight: number;
  paragraphSpacing: number;
  textAlign: 'left' | 'center' | 'right' | 'justify';
  showImages: boolean;
  fontFamily: keyof typeof FONT_FAMILIES;
  backgroundColor: keyof typeof BACKGROUND_COLORS;
}

/**
 * 集中式错误处理函数
 */
function handleError(error: unknown, context: string): void {
  // 确保错误是ReaderError类型，或将其包装为ReaderError
  const readerError = error instanceof ReaderError ? error : new ReaderError(
    error instanceof Error ? error.message : String(error),
    'UNEXPECTED_STATE', // 未知错误的默认代码
    { context, originalError: error }
  );

  console.error(`[阅读模式错误] ${context}:`, readerError); // 输出到控制台
  logger.logError(readerError); // 记录到IndexedDB

  // 根据错误代码显示用户友好的提示
  const message = userFriendlyMessages[readerError.code] || userFriendlyMessages.UNEXPECTED_STATE;
  Toast.error(message);
}

/**
 * 获取阅读模式设置
 */
async function fetchSettings(): Promise<ReadingModeSettings> {
  const [theme, fontSize, codeFontSize, codeTheme, lineHeight, paragraphSpacing, textAlign, showImages, fontFamily, backgroundColor] = await Promise.all([
    getStorage<'light' | 'dark'>(StorageKeys.THEME),
    getStorage<number>(StorageKeys.FONT_SIZE),
    getStorage<number>(StorageKeys.CODE_FONT_SIZE),
    getStorage<keyof typeof CODE_THEMES>(StorageKeys.CODE_THEME),
    getStorage<number>(StorageKeys.LINE_HEIGHT),
    getStorage<number>(StorageKeys.PARAGRAPH_SPACING),
    getStorage<'left' | 'center' | 'right' | 'justify'>(StorageKeys.TEXT_ALIGN),
    getStorage<boolean>(StorageKeys.SHOW_IMAGES),
    getStorage<keyof typeof FONT_FAMILIES>(StorageKeys.FONT_FAMILY),
    getStorage<keyof typeof BACKGROUND_COLORS>(StorageKeys.BACKGROUND_COLOR),
  ]);

  return {
    theme: theme ?? DEFAULT_SETTINGS.theme,
    fontSize: fontSize ?? DEFAULT_SETTINGS.fontSize,
    codeFontSize: codeFontSize ?? DEFAULT_SETTINGS.codeFontSize,
    codeTheme: codeTheme ?? DEFAULT_SETTINGS.codeTheme,
    lineHeight: lineHeight ?? DEFAULT_SETTINGS.lineHeight,
    paragraphSpacing: paragraphSpacing ?? DEFAULT_SETTINGS.paragraphSpacing,
    textAlign: textAlign ?? DEFAULT_SETTINGS.textAlign,
    showImages: showImages ?? DEFAULT_SETTINGS.showImages,
    fontFamily: fontFamily ?? DEFAULT_SETTINGS.fontFamily,
    backgroundColor: backgroundColor ?? DEFAULT_SETTINGS.backgroundColor,
  };
}

/**
 * 处理代码块
 */
async function handleCodeBlocks(container: HTMLElement | null, settings: ReadingModeSettings, forceReprocess: boolean = false) {
  if (!container) return;

  // 检查页面是否有代码块
  const preElements = container.querySelectorAll('pre');
  const existingContainers = container.querySelectorAll('.github-code-block, .code-block, .enhanced-code-container');
  const hasExistingCodeBlocks = existingContainers.length > 0;

  if (preElements.length === 0 && !hasExistingCodeBlocks) {
    console.log('页面没有代码块，跳过代码高亮库加载');
    return;
  }

  // 确定代码主题
  let codeTheme: string;
  switch (settings.codeTheme) {
    case 'github':
      codeTheme = settings.theme === 'dark' ? 'github-dark' : 'github-light';
      break;
    case 'one-dark':
      codeTheme = 'one-dark';
      break;
    case 'dracula':
      codeTheme = 'dracula';
      break;
    default:
      codeTheme = settings.theme === 'dark' ? 'github-dark' : 'github-light';
  }

  // 设置代码块主题类
  const themeClass = settings.theme === 'dark' ? 'dark-theme' : 'light-theme';
  container.classList.remove('dark-theme', 'light-theme');
  container.classList.add(themeClass);

  // 如果已有代码块且不需要强制重新处理，只更新样式
  if (hasExistingCodeBlocks && !forceReprocess) {
    console.log('更新现有代码块样式');
    // 更新代码块主题属性和主题类
    existingContainers.forEach(block => {
      block.setAttribute('data-code-theme', codeTheme);
      block.classList.remove('dark-theme', 'light-theme');
      block.classList.add(themeClass);
    });

    // 更新代码字体大小
    if (settings.codeFontSize) {
      const codeElements = container.querySelectorAll(
        '.github-code-block, .github-code-block code, .github-code-language, ' +
        '.github-code-copy-btn, .github-code-line-number, .github-inline-code'
      );

      codeElements.forEach(element => {
        if (element.classList.contains('github-code-line-number')) {
          // 行号字体稍小
          (element as HTMLElement).style.fontSize = `${Math.max(settings.codeFontSize - 2, 10)}px`;
        } else if (element.classList.contains('github-code-language') ||
          element.classList.contains('github-code-copy-btn')) {
          // 工具栏元素字体稍小
          (element as HTMLElement).style.fontSize = `${Math.max(settings.codeFontSize - 1, 11)}px`;
        } else {
          (element as HTMLElement).style.fontSize = `${settings.codeFontSize}px`;
        }
      });
    }
    return;
  }

  // 需要完全重新处理代码块
  console.log('开始处理代码块');
  try {
    // 获取所有代码块
    const codeBlocks = container.querySelectorAll('pre code');
    if (codeBlocks.length > 0) {
      console.log(`发现${codeBlocks.length}个代码块需要高亮处理`);

      // 加载highlight.js的CDN脚本
      if (!document.getElementById('highlight-js-cdn')) {
        const script = document.createElement('script');
        script.id = 'highlight-js-cdn';
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js';
        script.async = true;

        // 添加CSS
        const style = document.createElement('link');
        style.rel = 'stylesheet';
        style.href = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css';

        document.head.appendChild(style);

        script.onload = () => {
          console.log('highlight.js加载成功');
          // 应用高亮
          if (window.hljs) {
            codeBlocks.forEach(block => {
              window.hljs.highlightElement(block);
            });
          }
        };

        document.head.appendChild(script);
      } else if (window.hljs) {
        // 如果已加载，直接应用高亮
        codeBlocks.forEach(block => {
          window.hljs.highlightElement(block);
        });
      }
      if (!document.getElementById('highlight-js-cdn')) {
        const script = document.createElement('script');
        script.id = 'highlight-js-cdn';
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js';
        script.onload = () => {
          // 脚本加载完成后高亮代码块
          if (window.hljs) {
            codeBlocks.forEach(block => {
              window.hljs.highlightElement(block);
            });
            console.log('代码块高亮处理完成');
          }
        };
        document.head.appendChild(script);

        // 加载对应主题的CSS
        const linkCSS = document.createElement('link');
        linkCSS.rel = 'stylesheet';
        linkCSS.href = `https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/${codeTheme}.min.css`;
        document.head.appendChild(linkCSS);
      } else {
        // 如果脚本已加载，直接应用高亮
        if (window.hljs) {
          codeBlocks.forEach(block => {
            window.hljs.highlightElement(block);
          });
          console.log('代码块高亮处理完成');
        }
      }
    }
  } catch (error) {
    console.error('处理代码块时发生错误:', error);
  }
}

/**
 * 应用阅读模式样式
 */
async function applyStyles(settings: ReadingModeSettings) {
  // 移除旧样式
  const oldStyles = document.getElementById('reading-mode-dynamic-styles');
  if (oldStyles) {
    oldStyles.remove();
  }

  // 创建新样式
  const readerStyles = document.createElement('style');
  readerStyles.id = 'reading-mode-dynamic-styles';

  // 应用字体
  const fontFamily = FONT_FAMILIES[settings.fontFamily] || FONT_FAMILIES.system;

  // 应用背景色
  const backgroundColor = BACKGROUND_COLORS[settings.backgroundColor] || BACKGROUND_COLORS.white;
  const darkBackgroundColor = BACKGROUND_COLORS.dark;

  // 设置CSS变量
  readerStyles.textContent = `
    :root {
      --reader-font-size: ${settings.fontSize}px;
      --reader-line-height: ${settings.lineHeight};
      --reader-paragraph-spacing: ${settings.paragraphSpacing}px;
      --reader-font-family: ${fontFamily};
      --reader-background-color: ${backgroundColor};
      --reader-dark-background-color: ${darkBackgroundColor};
      --reader-text-color: #333;
      --reader-dark-text-color: #eee;
    }
    
    #reading-mode-container {
      font-family: var(--reader-font-family);
      font-size: var(--reader-font-size);
      line-height: var(--reader-line-height);
      background-color: var(--reader-background-color);
      color: var(--reader-text-color);
      padding: 2rem;
      max-width: 800px;
      margin: 0 auto;
      text-align: ${settings.textAlign};
    }
    
    .dark #reading-mode-container {
      background-color: var(--reader-dark-background-color);
      color: var(--reader-dark-text-color);
    }
    
    #reading-mode-container p {
      margin-bottom: var(--reader-paragraph-spacing);
    }
    
    #reading-mode-container img {
      max-width: 100%;
      height: auto;
      display: ${settings.showImages ? 'block' : 'none'};
      margin: 1rem auto;
    }
    
    .reader-content-container {
      color: var(--reader-text-color);
    }
    
    .dark .reader-content-container {
      color: var(--reader-dark-text-color);
    }
  `;

  document.head.appendChild(readerStyles);
}

/**
 * 创建浮动按钮
 */
function createFloatingButton() {
  // 检查是否已存在浮动按钮
  if (document.getElementById('reading-mode-floating-button')) {
    return;
  }

  const button = document.createElement('button');
  button.id = 'reading-mode-floating-button';
  button.textContent = '阅读模式';
  button.style.position = 'fixed';
  button.style.bottom = '20px';
  button.style.right = '20px';
  button.style.zIndex = '9999';
  button.style.padding = '8px 12px';
  button.style.backgroundColor = '#4285f4';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.borderRadius = '4px';
  button.style.cursor = 'pointer';
  button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';

  button.addEventListener('click', () => {
    toggleReadingMode();
  });

  document.body.appendChild(button);
}

/**
 * 移除浮动按钮
 */
function removeFloatingButton() {
  const button = document.getElementById('reading-mode-floating-button');
  if (button) {
    button.remove();
  }
}

function getMarkdownWorkerManager(): MarkdownWorkerManager {
  if (!markdownWorkerManager) {
    markdownWorkerManager = new MarkdownWorkerManager();
  }
  return markdownWorkerManager;
}

/**
 * 切换阅读模式
 * @returns 切换后的阅读模式状态
 */
export async function toggleReadingMode(): Promise<boolean> {
  try {
    if (isReaderMode) {
      // 禁用阅读模式
      disableReadingMode();
      isReaderMode = false;
      console.log('返回当前阅读模式状态:', isReaderMode);
      return isReaderMode;
    } else {
      // 启用阅读模式
      isReaderMode = await enableReadingMode();
      console.log('返回当前阅读模式状态:', isReaderMode);
      return isReaderMode;
    }
  } catch (error) {
    // 处理错误
    isReaderMode = false;
    console.error('切换阅读模式时发生错误:', error);

    // 记录详细错误信息以帮助调试
    if (error instanceof RenderError) {
      console.error('[阅读模式错误] 启用阅读模式:', error);
    } else if (error instanceof Error) {
      console.error('[阅读模式错误] 未捕获错误:', error.message, error.stack);
    }

    // 确保在发生错误时恢复到原始状态
    try {
      if (originalContent.length > 0) {
        disableReadingMode();
      }
    } catch (cleanupError) {
      console.error('清理阅读模式失败:', cleanupError);
    }

    // 返回当前状态（应该是false）
    return isReaderMode;
  }
}

/**
 * 启用阅读模式
 */
async function enableReadingMode(): Promise<boolean> {
  try {
    // 保存当前滚动位置和内容
    originalScrollY = window.scrollY;
    originalContent = Array.from(document.body.childNodes);

    // 提取内容
    const extractor = new ReadabilityExtractor();
    const extractionResult = await extractor.extract(document);
    console.log('内容提取完成');

    if (!extractionResult || !extractionResult.content) {
      throw new ReaderError('内容提取失败', 'EXTRACTION_FAILED');
    }

    extractedContent = extractionResult.content;

    // 获取MarkdownWorkerManager实例
    const workerManager = getMarkdownWorkerManager();

    try {
      // 转换为Markdown（异步）
      const markdown = await workerManager.convertToMarkdown(extractedContent);

      // 渲染阅读模式
      renderReadingMode(markdown, extractionResult.title || document.title);
      return true;
    } catch (markdownError) {
      console.error('Markdown转换失败:', markdownError);

      // 如果Markdown转换失败，尝试直接使用HTML内容作为降级方案
      console.log('尝试使用直接HTML渲染作为降级方案');
      renderReadingModeWithHtml(extractedContent, extractionResult.title || document.title);
      return true;
    }
  } catch (error) {
    // 转换为RenderError
    if (error instanceof Error) {
      throw new RenderError('Markdown 转换过程中发生错误', { cause: error });
    } else {
      throw new RenderError('启用阅读模式时发生未知错误');
    }
  }
}

/**
 * 禁用阅读模式
 */
function disableReadingMode(): void {
  // 清空body
  document.body.innerHTML = '';

  // 恢复原始内容
  originalContent.forEach(node => {
    document.body.appendChild(node.cloneNode(true));
  });

  // 恢复滚动位置
  window.scrollTo(0, originalScrollY);

  // 重置变量
  originalContent = [];
  extractedContent = '';
}

/**
 * 渲染阅读模式（使用Markdown）
 */
function renderReadingMode(markdown: string, title: string): void {
  // 添加Material Design 3样式
  addReadingModeStyles();

  // 创建阅读模式主容器
  const mainContainer = document.createElement('div');
  mainContainer.id = 'reading-mode-container';

  // 创建顶部控制栏
  const controlBar = createControlBar();
  mainContainer.appendChild(controlBar);

  // 创建内容容器
  const contentContainer = document.createElement('div');
  contentContainer.className = 'reading-mode-content';

  // 添加标题
  const titleElement = document.createElement('h1');
  titleElement.textContent = title;
  contentContainer.appendChild(titleElement);

  // 添加元信息（如果有的话）
  const metaElement = document.createElement('div');
  metaElement.className = 'reading-mode-meta';

  // 添加发布时间和来源信息
  const currentUrl = window.location.href;
  const domain = new URL(currentUrl).hostname;
  metaElement.innerHTML = `
    <span>来源: ${domain}</span>
    <span>阅读时间: ${new Date().toLocaleDateString()}</span>
  `;
  contentContainer.appendChild(metaElement);

  // 创建文章内容容器
  const articleElement = document.createElement('div');
  articleElement.className = 'reading-mode-article';

  // 渲染Markdown内容
  articleElement.innerHTML = renderMarkdown(markdown);

  contentContainer.appendChild(articleElement);
  mainContainer.appendChild(contentContainer);

  // 清空body并添加阅读视图
  document.body.innerHTML = '';
  document.body.appendChild(mainContainer);

  // 滚动到顶部
  window.scrollTo(0, 0);
}

/**
 * 降级方案：直接使用HTML渲染阅读模式
 */
function renderReadingModeWithHtml(html: string, title: string): void {
  // 添加Material Design 3样式
  addReadingModeStyles();

  // 创建阅读模式主容器
  const mainContainer = document.createElement('div');
  mainContainer.id = 'reading-mode-container';

  // 创建内容容器
  const contentContainer = document.createElement('div');
  contentContainer.className = 'reading-mode-content';

  // 添加标题
  const titleElement = document.createElement('h1');
  titleElement.textContent = title;
  contentContainer.appendChild(titleElement);

  // 添加元信息
  const metaElement = document.createElement('div');
  metaElement.className = 'reading-mode-meta';

  const currentUrl = window.location.href;
  const domain = new URL(currentUrl).hostname;
  metaElement.innerHTML = `
    <span>来源: ${domain}</span>
    <span>阅读时间: ${new Date().toLocaleDateString()}</span>
  `;
  contentContainer.appendChild(metaElement);

  // 创建文章内容容器
  const articleElement = document.createElement('div');
  articleElement.className = 'reading-mode-article';

  // 直接使用HTML内容
  articleElement.innerHTML = html;

  contentContainer.appendChild(articleElement);
  mainContainer.appendChild(contentContainer);

  // 清空body并添加阅读视图
  document.body.innerHTML = '';
  document.body.appendChild(mainContainer);

  // 滚动到顶部
  window.scrollTo(0, 0);
}

/**
 * 添加Material Design 3阅读模式样式
 */
function addReadingModeStyles(): void {
  // 首先加载Material Design 3设计令牌
  const designTokensStyle = document.createElement('style');
  designTokensStyle.id = 'md3-design-tokens';
  designTokensStyle.textContent = `
    /* Material Design 3 设计令牌 */
    :root {
      /* 颜色系统 */
      --md3-sys-color-primary: #1976d2;
      --md3-sys-color-on-primary: #ffffff;
      --md3-sys-color-primary-container: #e3f2fd;
      --md3-sys-color-on-primary-container: #0d47a1;
      --md3-sys-color-surface: #fefbff;
      --md3-sys-color-surface-container: #f3f3f3;
      --md3-sys-color-surface-container-high: #ececec;
      --md3-sys-color-on-surface: #1c1b1f;
      --md3-sys-color-on-surface-variant: #49454f;
      --md3-sys-color-outline: #79747e;
      --md3-sys-color-outline-variant: #cac4d0;

      /* 排版系统 */
      --md3-sys-typescale-headline-large-font-family: 'Google Sans', 'Roboto', system-ui, sans-serif;
      --md3-sys-typescale-headline-large-size: 32px;
      --md3-sys-typescale-headline-large-weight: 400;
      --md3-sys-typescale-headline-large-line-height: 40px;
      --md3-sys-typescale-headline-large-tracking: 0px;

      --md3-sys-typescale-headline-medium-font-family: 'Google Sans', 'Roboto', system-ui, sans-serif;
      --md3-sys-typescale-headline-medium-size: 28px;
      --md3-sys-typescale-headline-medium-weight: 400;
      --md3-sys-typescale-headline-medium-line-height: 36px;
      --md3-sys-typescale-headline-medium-tracking: 0px;

      --md3-sys-typescale-headline-small-font-family: 'Google Sans', 'Roboto', system-ui, sans-serif;
      --md3-sys-typescale-headline-small-size: 24px;
      --md3-sys-typescale-headline-small-weight: 400;
      --md3-sys-typescale-headline-small-line-height: 32px;
      --md3-sys-typescale-headline-small-tracking: 0px;

      --md3-sys-typescale-title-medium-font-family: 'Roboto', system-ui, sans-serif;
      --md3-sys-typescale-title-medium-size: 16px;
      --md3-sys-typescale-title-medium-weight: 500;
      --md3-sys-typescale-title-medium-line-height: 24px;
      --md3-sys-typescale-title-medium-tracking: 0.15px;

      --md3-sys-typescale-body-large-font-family: 'Roboto', system-ui, sans-serif;
      --md3-sys-typescale-body-large-size: 16px;
      --md3-sys-typescale-body-large-line-height: 24px;
      --md3-sys-typescale-body-large-tracking: 0.5px;

      --md3-sys-typescale-body-medium-font-family: 'Roboto', system-ui, sans-serif;
      --md3-sys-typescale-body-medium-size: 14px;
      --md3-sys-typescale-body-medium-line-height: 20px;
      --md3-sys-typescale-body-medium-tracking: 0.25px;

      /* 形状系统 */
      --md3-sys-shape-corner-extra-small: 4px;
      --md3-sys-shape-corner-small: 8px;
      --md3-sys-shape-corner-medium: 12px;
      --md3-sys-shape-corner-large: 16px;

      /* 阴影系统 */
      --md3-sys-elevation-level1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
      --md3-sys-elevation-level2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
      --md3-sys-elevation-level3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);

      /* 动画系统 */
      --md3-sys-motion-duration-short2: 200ms;
      --md3-sys-motion-duration-medium2: 300ms;
      --md3-sys-motion-duration-long2: 500ms;
      --md3-sys-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
      --md3-sys-motion-easing-emphasized: cubic-bezier(0.2, 0, 0, 1);
    }

    /* 深色主题 */
    @media (prefers-color-scheme: dark) {
      :root {
        --md3-sys-color-primary: #90caf9;
        --md3-sys-color-on-primary: #003258;
        --md3-sys-color-primary-container: #004881;
        --md3-sys-color-on-primary-container: #cce7ff;
        --md3-sys-color-surface: #10131c;
        --md3-sys-color-surface-container: #1e2129;
        --md3-sys-color-surface-container-high: #282a31;
        --md3-sys-color-on-surface: #e2e2e9;
        --md3-sys-color-on-surface-variant: #c4c7c5;
        --md3-sys-color-outline: #8e918f;
        --md3-sys-color-outline-variant: #44474e;
      }
    }
  `;
  document.head.appendChild(designTokensStyle);

  // 然后加载阅读模式样式
  const readingModeStylesUrl = chrome.runtime.getURL('src/content/styles/reading-mode-md3.css');
  const linkElement = document.createElement('link');
  linkElement.rel = 'stylesheet';
  linkElement.href = readingModeStylesUrl;
  linkElement.id = 'reading-mode-md3-styles';
  document.head.appendChild(linkElement);
}

/**
 * 增强的Markdown渲染器
 * 支持更丰富的格式，更好地保留原文样式
 */
function renderMarkdown(markdown: string): string {
  if (!markdown) {
    return '<p>内容为空</p>';
  }

  // 分割成行进行处理
  const lines = markdown.split('\n');
  const result: string[] = [];
  let inCodeBlock = false;
  let codeBlockContent: string[] = [];
  let codeBlockLanguage = '';
  let inList = false;
  let listItems: string[] = [];
  let listType = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // 处理代码块
    if (trimmedLine.startsWith('```')) {
      if (inCodeBlock) {
        // 结束代码块
        const codeContent = codeBlockContent.join('\n');
        const escapedCode = escapeHtml(codeContent);
        result.push(`<pre class="language-${codeBlockLanguage}"><code>${escapedCode}</code></pre>`);
        inCodeBlock = false;
        codeBlockContent = [];
        codeBlockLanguage = '';
      } else {
        // 开始代码块
        inCodeBlock = true;
        codeBlockLanguage = trimmedLine.substring(3).trim() || 'text';
      }
      continue;
    }

    if (inCodeBlock) {
      codeBlockContent.push(line);
      continue;
    }

    // 处理列表
    const listMatch = line.match(/^(\s*)([-*+]|\d+\.)\s+(.+)$/);
    if (listMatch) {
      const [, indent, marker, content] = listMatch;
      const isOrdered = /\d+\./.test(marker);
      const currentListType = isOrdered ? 'ol' : 'ul';

      if (!inList) {
        inList = true;
        listType = currentListType;
        listItems = [];
      } else if (listType !== currentListType) {
        // 结束当前列表，开始新列表
        result.push(`<${listType}>${listItems.map(item => `<li>${item}</li>`).join('')}</${listType}>`);
        listType = currentListType;
        listItems = [];
      }

      listItems.push(processInlineElements(content));
      continue;
    } else if (inList) {
      // 结束列表
      result.push(`<${listType}>${listItems.map(item => `<li>${item}</li>`).join('')}</${listType}>`);
      inList = false;
      listItems = [];
      listType = '';
    }

    // 处理标题
    const headingMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
    if (headingMatch) {
      const level = headingMatch[1].length;
      const content = processInlineElements(headingMatch[2]);
      result.push(`<h${level}>${content}</h${level}>`);
      continue;
    }

    // 处理引用
    if (trimmedLine.startsWith('>')) {
      const quoteContent = trimmedLine.substring(1).trim();
      const processedContent = processInlineElements(quoteContent);
      result.push(`<blockquote><p>${processedContent}</p></blockquote>`);
      continue;
    }

    // 处理分隔线
    if (trimmedLine.match(/^[-*_]{3,}$/)) {
      result.push('<hr>');
      continue;
    }

    // 处理普通段落
    if (trimmedLine) {
      const processedContent = processInlineElements(trimmedLine);
      result.push(`<p>${processedContent}</p>`);
    } else {
      // 空行
      result.push('');
    }
  }

  // 结束可能剩余的列表
  if (inList) {
    result.push(`<${listType}>${listItems.map(item => `<li>${item}</li>`).join('')}</${listType}>`);
  }

  return result.join('\n');
}

/**
 * 处理行内元素（粗体、斜体、链接、图片、代码等）
 */
function processInlineElements(text: string): string {
  let result = escapeHtml(text);

  // 处理图片（必须在链接之前处理）
  result = result.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, src) => {
    return `<figure class="reader-figure"><img src="${src}" alt="${alt}" loading="lazy"><figcaption>${alt}</figcaption></figure>`;
  });

  // 处理链接
  result = result.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
    return `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`;
  });

  // 处理行内代码（必须在粗体斜体之前处理）
  result = result.replace(/`([^`]+)`/g, '<code>$1</code>');

  // 处理粗体（** 或 __）
  result = result.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
  result = result.replace(/__([^_]+)__/g, '<strong>$1</strong>');

  // 处理斜体（* 或 _）
  result = result.replace(/\*([^*]+)\*/g, '<em>$1</em>');
  result = result.replace(/_([^_]+)_/g, '<em>$1</em>');

  // 处理删除线
  result = result.replace(/~~([^~]+)~~/g, '<del>$1</del>');

  // 处理下划线
  result = result.replace(/\+\+([^+]+)\+\+/g, '<u>$1</u>');

  return result;
}

/**
 * HTML 转义
 */
function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * 创建阅读模式顶部控制栏
 */
function createControlBar(): HTMLElement {
  const controlBar = document.createElement('div');
  controlBar.className = 'reading-mode-control-bar';
  controlBar.innerHTML = `
    <div class="control-bar-content">
      <button class="control-btn theme-toggle" title="切换主题">🌙</button>
      <button class="control-btn font-size-up" title="增大字体">A+</button>
      <button class="control-btn font-size-down" title="减小字体">A-</button>
      <button class="control-btn preset-toggle" title="阅读预设">⚙️</button>
      <button class="control-btn close-btn" title="退出阅读模式">✕</button>
    </div>
    <div class="preset-panel" style="display: none;">
      <div class="preset-options">
        <label>字体大小: <input type="range" id="font-size-slider" min="12" max="24" value="16"></label>
        <label>行高: <input type="range" id="line-height-slider" min="1.2" max="2.0" step="0.1" value="1.6"></label>
        <label>段落间距: <input type="range" id="paragraph-spacing-slider" min="8" max="32" value="16"></label>
        <select id="font-family-select">
          <option value="default">默认字体</option>
          <option value="serif">衬线字体</option>
          <option value="sans-serif">无衬线字体</option>
        </select>
        <select id="background-select">
          <option value="white">白色背景</option>
          <option value="sepia">护眼模式</option>
          <option value="dark">深色模式</option>
        </select>
      </div>
    </div>
  `;

  // 添加事件监听器
  setupControlBarEvents(controlBar);

  return controlBar;
}

/**
 * 设置控制栏事件
 */
function setupControlBarEvents(controlBar: HTMLElement): void {
  const container = document.getElementById('reading-mode-container');
  if (!container) return;

  // 主题切换
  const themeToggle = controlBar.querySelector('.theme-toggle');
  themeToggle?.addEventListener('click', () => {
    const isDark = container.classList.contains('dark-theme');
    container.classList.toggle('dark-theme', !isDark);
    container.classList.toggle('light-theme', isDark);
    (themeToggle as HTMLElement).textContent = isDark ? '🌙' : '☀️';
  });

  // 字体大小调整
  const fontSizeUp = controlBar.querySelector('.font-size-up');
  const fontSizeDown = controlBar.querySelector('.font-size-down');
  
  fontSizeUp?.addEventListener('click', () => {
    const currentSize = parseInt(getComputedStyle(container).fontSize);
    container.style.fontSize = `${Math.min(currentSize + 2, 24)}px`;
  });
  
  fontSizeDown?.addEventListener('click', () => {
    const currentSize = parseInt(getComputedStyle(container).fontSize);
    container.style.fontSize = `${Math.max(currentSize - 2, 12)}px`;
  });

  // 预设面板切换
  const presetToggle = controlBar.querySelector('.preset-toggle');
  const presetPanel = controlBar.querySelector('.preset-panel') as HTMLElement;
  
  presetToggle?.addEventListener('click', () => {
    const isVisible = presetPanel.style.display !== 'none';
    presetPanel.style.display = isVisible ? 'none' : 'block';
  });

  // 退出阅读模式
  const closeBtn = controlBar.querySelector('.close-btn');
  closeBtn?.addEventListener('click', () => {
    disableReadingMode();
  });

  // 预设控制事件
  setupPresetControls(controlBar, container);
}

/**
 * 设置预设控制事件
 */
function setupPresetControls(controlBar: HTMLElement, container: HTMLElement): void {
  // 字体大小滑块
  const fontSizeSlider = controlBar.querySelector('#font-size-slider') as HTMLInputElement;
  fontSizeSlider?.addEventListener('input', (e) => {
    const size = (e.target as HTMLInputElement).value;
    container.style.fontSize = `${size}px`;
  });

  // 行高滑块
  const lineHeightSlider = controlBar.querySelector('#line-height-slider') as HTMLInputElement;
  lineHeightSlider?.addEventListener('input', (e) => {
    const height = (e.target as HTMLInputElement).value;
    container.style.lineHeight = height;
  });

  // 段落间距滑块
  const paragraphSpacingSlider = controlBar.querySelector('#paragraph-spacing-slider') as HTMLInputElement;
  paragraphSpacingSlider?.addEventListener('input', (e) => {
    const spacing = (e.target as HTMLInputElement).value;
    const style = document.getElementById('reading-dynamic-spacing') || document.createElement('style');
    style.id = 'reading-dynamic-spacing';
    style.textContent = `.reading-mode-article p { margin-bottom: ${spacing}px; }`;
    if (!style.parentNode) document.head.appendChild(style);
  });

  // 字体选择
  const fontFamilySelect = controlBar.querySelector('#font-family-select') as HTMLSelectElement;
  fontFamilySelect?.addEventListener('change', (e) => {
    const fontFamily = (e.target as HTMLSelectElement).value;
    const fontMap: Record<string, string> = {
      'default': 'system-ui, -apple-system, sans-serif',
      'serif': 'Georgia, "Times New Roman", serif',
      'sans-serif': 'Arial, Helvetica, sans-serif'
    };
    container.style.fontFamily = fontMap[fontFamily] || fontMap.default;
  });

  // 背景选择
  const backgroundSelect = controlBar.querySelector('#background-select') as HTMLSelectElement;
  backgroundSelect?.addEventListener('change', (e) => {
    const background = (e.target as HTMLSelectElement).value;
    container.classList.remove('white-bg', 'sepia-bg', 'dark-bg');
    container.classList.add(`${background}-bg`);
  });
}

// 监听存储变化
chrome.storage.onChanged.addListener(async (changes) => {
  // 如果不在阅读模式下，不应用样式
  if (!isReaderMode) return;

  const container = document.getElementById('reading-mode-container');
  if (!container) return;

  const settings = await fetchSettings();
  await applyStyles(settings);
}); 