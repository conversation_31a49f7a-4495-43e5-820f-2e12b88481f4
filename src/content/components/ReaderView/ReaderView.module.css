/* ReaderView.module.css */

/* 使用CSS Modules隔离样式，避免污染全局作用域 */
/* 使用新的变量命名空间，防止与全局变量冲突 */

.readerView {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  background-color: var(--reader-bg-color); /* 由 content.ts 统一管理 */
  color: var(--reader-text-color); /* 由 content.ts 统一管理 */
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  overflow: hidden;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 浅色主题变量 (不再需要，由 content.ts 统一管理)*/
/*
.readerView {
  --reader-bg-color: #ffffff;
  --reader-text-color: #333333;
  --reader-link-color: #0066cc;
  --reader-border-color: #e0e0e0;
  --reader-toolbar-bg: rgba(255, 255, 255, 0.95);
  --reader-toolbar-color: #333333;
  --reader-highlight-color: #ffeb3b;
  --reader-highlight-bg: rgba(255, 235, 59, 0.2);
  --reader-blockquote-color: #777777;
  --reader-blockquote-bg: #f9f9f9;
  --reader-blockquote-border: #e0e0e0;
  --reader-code-bg: #f5f5f5;
  --reader-code-color: #c92a2a;
  --reader-shadow-color: rgba(0, 0, 0, 0.15);
  --reader-loading-color: #666666;
  --reader-button-hover: #f0f0f0;
  --reader-button-active: #e0e0e0;
}
*/

/* 深色主题变量 (不再需要，由 content.ts 统一管理)*/
/*
.readerView[data-theme="dark"] {
  --reader-bg-color: #1a1a1a;
  --reader-text-color: #e0e0e0;
  --reader-link-color: #79b8ff;
  --reader-border-color: #444444;
  --reader-toolbar-bg: rgba(26, 26, 26, 0.95);
  --reader-toolbar-color: #e0e0e0;
  --reader-highlight-color: #ffcc00;
  --reader-highlight-bg: rgba(255, 204, 0, 0.2);
  --reader-blockquote-color: #b0b0b0;
  --reader-blockquote-bg: #2a2a2a;
  --reader-blockquote-border: #444444;
  --reader-code-bg: #2d2d2d;
  --reader-code-color: #ff8383;
  --reader-shadow-color: rgba(0, 0, 0, 0.3);
  --reader-loading-color: #999999;
  --reader-button-hover: #333333;
  --reader-button-active: #3a3a3a;
}
*/

/* 加载中状态 */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--reader-loading-color);
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--reader-text-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 错误状态 */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 0 20px;
}

.errorContainer h3 {
  font-size: 24px;
  margin-bottom: 12px;
}

.errorContainer p {
  margin-bottom: 24px;
  color: var(--reader-blockquote-color);
  max-width: 500px;
}

.retryButton {
  background-color: var(--reader-link-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: opacity 0.2s;
}

.retryButton:hover {
  opacity: 0.9;
}

/* 空状态 */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.emptyState p {
  margin-bottom: 24px;
  color: var(--reader-blockquote-color);
}

.extractButton {
  background-color: var(--reader-link-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: opacity 0.2s;
}

.extractButton:hover {
  opacity: 0.9;
}

/* 工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--reader-toolbar-bg);
  border-bottom: 1px solid var(--reader-border-color);
  position: sticky;
  top: 0;
  z-index: 10;
  transition: background-color 0.3s;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* 关闭按钮 */
.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--reader-text-color);
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 0;
  margin-right: 10px;
  transition: background-color 0.2s;
}

.closeButton:hover {
  background-color: var(--reader-button-hover);
}

.closeButton:active {
  background-color: var(--reader-button-active);
}

/* 主题切换按钮 (不再需要)*/
/*
.themeButton {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--reader-text-color);
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 0;
  transition: background-color 0.2s;
}

.themeButton:hover {
  background-color: var(--reader-button-hover);
}

.themeButton:active {
  background-color: var(--reader-button-active);
}
*/

/* 内容容器 */
.contentContainer {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
  scroll-behavior: smooth;
}

/* 文章容器 - 居中布局并限制最大宽度 */
.articleContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px 24px 60px;
}

/* 文章标题 */
.articleTitle {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 16px;
  line-height: 1.3;
}

/* 文章元数据 */
.articleMeta {
  font-size: 14px;
  color: var(--reader-blockquote-color);
  margin-bottom: 20px;
}

.articleMeta span {
  margin-right: 15px;
  opacity: 0.8;
}

/* 文章内容 */
.articleContent {
  font-size: 17px;
  line-height: 1.7;
  color: var(--reader-text-color);
}

.articleContent p {
  margin-bottom: 1em;
  text-align: justify;
}

.articleContent h1 {
  font-size: 2.2em;
  font-weight: 700;
  margin-top: 1.5em;
  margin-bottom: 0.8em;
}

.articleContent h2 {
  font-size: 1.8em;
  font-weight: 700;
  margin-top: 1.5em;
  margin-bottom: 0.8em;
}

.articleContent h3 {
  font-size: 1.5em;
  font-weight: 700;
  margin-top: 1.2em;
  margin-bottom: 0.6em;
}

.articleContent h4,
.articleContent h5,
.articleContent h6 {
  font-size: 1.2em;
  font-weight: 700;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.articleContent a {
  color: var(--reader-link-color);
  text-decoration: none;
  border-bottom: 1px solid var(--reader-link-color);
  transition: border-color 0.2s ease;
}

.articleContent a:hover {
  border-bottom-color: transparent;
}

.articleContent img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1.5em auto;
  border-radius: 8px;
  box-shadow: 0 4px 10px var(--reader-shadow-color);
}

.articleContent blockquote {
  border-left: 4px solid var(--reader-blockquote-border);
  padding-left: 1em;
  margin: 1em 0;
  color: var(--reader-blockquote-color);
  font-style: italic;
  background-color: var(--reader-blockquote-bg);
  padding: 1em 1.5em;
  border-radius: 4px;
}

.articleContent pre {
  background-color: var(--reader-code-bg);
  padding: 1em;
  border-radius: 8px;
  overflow-x: auto;
  font-family: "Fira Code", monospace;
  font-size: 0.9em;
  line-height: 1.5;
  color: var(--reader-code-color);
  margin-bottom: 1em;
}

.articleContent pre code {
  font-size: 1em;
  line-height: 1.5;
}

.articleContent ul,
.articleContent ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.articleContent li {
  margin-bottom: 0.5em;
}

@media (max-width: 768px) {
  .articleContainer {
    padding: 15px 20px 40px;
  }

  .articleTitle {
    font-size: 24px;
  }

  .articleContent {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .articleMeta {
    flex-direction: column;
    align-items: flex-start;
  }

  .articleMeta span {
    margin-right: 0;
    margin-bottom: 5px;
  }
}
