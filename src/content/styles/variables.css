/*
 * CSS 变量系统 - 基于 Obsidian Clipper 设计理念
 * 使用更加系统化的命名约定，将颜色变量分为基础色、语义色和组件色三个层级
 */

:root {
  /* ===== 基础色彩 ===== */
  --color-white: #ffffff;
  --color-black: #000000;

  /* 灰度色阶 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* 品牌色阶 */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;

  /* ===== 语义色彩 ===== */
  /* 浅色主题默认值 */
  --color-text: var(--color-gray-900);
  --color-text-muted: var(--color-gray-600);
  --color-text-faint: var(--color-gray-400);
  --color-background: var(--color-white);
  --color-background-alt: var(--color-gray-50);
  --color-border: var(--color-gray-200);
  --color-accent: var(--color-primary-600);
  --color-accent-muted: var(--color-primary-100);
  --color-selection-bg: rgba(59, 130, 246, 0.2);
  --color-selection-text: inherit;

  /* ===== 阅读模式组件色彩 ===== */
  --reading-bg-color: var(--color-background);
  --reading-content-bg-color: var(--color-white);
  --reading-text-color: var(--color-text);
  --reading-muted-color: var(--color-text-muted);
  --reading-border-color: var(--color-border);
  --reading-accent-color: var(--color-accent);
  --reading-link-color: var(--color-primary-700);
  --reading-link-hover-color: var(--color-primary-800);
  --reading-selection-bg: var(--color-selection-bg);
  --reading-selection-text: var(--color-selection-text);
  --reading-blockquote-bg: var(--color-gray-50);
  --reading-blockquote-border: var(--color-gray-300);
  --reading-blockquote-text: var(--color-gray-700);
  --reading-code-bg: var(--color-gray-50);
  --reading-code-text: var(--color-gray-800);
  --reading-code-border: var(--color-gray-200);
  --reading-table-border: var(--color-gray-200);
  --reading-table-header-bg: var(--color-gray-50);
  --reading-table-row-odd: var(--color-white);
  --reading-table-row-even: var(--color-gray-50);
  --reading-scrollbar-track: transparent;
  --reading-scrollbar-thumb: rgba(0, 0, 0, 0.2);
  --reading-scrollbar-thumb-hover: rgba(0, 0, 0, 0.3);
  --reading-content-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  /* ===== 排版变量 ===== */
  --font-text: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-heading: var(--font-text);
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Consolas, "Liberation Mono", monospace;

  --reading-font-family: var(--font-text);
  --reading-heading-font-family: var(--font-heading);
  --reading-code-font-family: var(--font-mono);

  /* 默认尺寸 */
  --reading-page-width: min(720px, 90vw);
  --reading-content-padding: clamp(1rem, 5vw, 2rem);
  --reading-font-size: clamp(16px, 1rem + 0.2vw, 20px);
  --reading-line-height: 1.8;
  --reading-letter-spacing: 0.01em;
  --reading-paragraph-spacing: 1.5rem;
  --reading-first-line-indent: 0;

  /* 代码块默认尺寸 - 使用独立的命名空间 */
  --reading-code-font-size: 14px;
  --reading-code-line-height: 1.5;
  --reading-code-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  --reading-code-shadow-hover: 0 2px 4px rgba(0, 0, 0, 0.1);

  /* 保留原有变量以兼容现有代码，但将其与阅读模式变量隔离 */
  --code-font-size: var(--reading-code-font-size);
  --code-line-height: var(--reading-code-line-height);
  --code-shadow: var(--reading-code-shadow);
  --code-shadow-hover: var(--reading-code-shadow-hover);
}

/* 深色主题变量 */
.dark-theme {
  /* 语义色彩 - 深色主题 */
  --color-text: var(--color-gray-100);
  --color-text-muted: var(--color-gray-400);
  --color-text-faint: var(--color-gray-600);
  --color-background: #121212;
  --color-background-alt: #1e1e1e;
  --color-border: #333333;
  --color-accent: var(--color-primary-400);
  --color-accent-muted: rgba(56, 189, 248, 0.2);
  --color-selection-bg: rgba(59, 130, 246, 0.3);

  /* 阅读模式组件色彩 - 深色主题 */
  --reading-bg-color: var(--color-background);
  --reading-content-bg-color: #1e1e1e;
  --reading-text-color: rgba(255, 255, 255, 0.87);
  --reading-muted-color: rgba(255, 255, 255, 0.6);
  --reading-border-color: rgba(255, 255, 255, 0.1);
  --reading-accent-color: #90caf9;
  --reading-link-color: #90caf9;
  --reading-link-hover-color: #bbdefb;
  --reading-selection-bg: var(--color-selection-bg);
  --reading-blockquote-bg: rgba(255, 255, 255, 0.05);
  --reading-blockquote-border: rgba(255, 255, 255, 0.1);
  --reading-blockquote-text: rgba(255, 255, 255, 0.7);
  --reading-code-bg: #2d2d2d;
  --reading-code-text: #e0e0e0;
  --reading-code-border: #444444;
  --reading-table-border: #444444;
  --reading-table-header-bg: #333333;
  --reading-table-row-odd: #262626;
  --reading-table-row-even: #2a2a2a;
  --reading-scrollbar-track: transparent;
  --reading-scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --reading-scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
  --reading-content-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);

  /* 代码块变量 - 深色主题 */
  --reading-code-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  --reading-code-shadow-hover: 0 2px 6px rgba(0, 0, 0, 0.3);

  /* 保持与阅读模式变量的隔离 */
  --code-shadow: var(--reading-code-shadow);
  --code-shadow-hover: var(--reading-code-shadow-hover);
}
