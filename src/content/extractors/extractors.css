/* 增强提取器样式 */

/* 表格样式 */
.enhanced-table-container {
  margin: 2em 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-responsive {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.enhanced-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 0.95em;
}

.enhanced-table th,
.enhanced-table td {
  padding: 0.75em 1em;
  text-align: left;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.enhanced-table thead {
  background-color: rgba(0, 0, 0, 0.05);
}

.enhanced-table th {
  font-weight: 600;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.enhanced-table .header-row {
  background-color: rgba(0, 0, 0, 0.05);
}

.enhanced-table .even-row {
  background-color: rgba(0, 0, 0, 0.02);
}

.enhanced-table .odd-row {
  background-color: transparent;
}

.enhanced-table .first-column {
  font-weight: 500;
}

.table-controls {
  display: flex;
  justify-content: space-between;
  padding: 0.75em 1em;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.table-search-input {
  padding: 0.5em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 0.9em;
  width: 200px;
}

.table-sort-select {
  padding: 0.5em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 0.9em;
  background-color: white;
}

/* 暗色主题表格样式 */
.dark-theme .enhanced-table-container {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark-theme .enhanced-table th,
.dark-theme .enhanced-table td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .enhanced-table thead {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-theme .enhanced-table th {
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .enhanced-table .header-row {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-theme .enhanced-table .even-row {
  background-color: rgba(255, 255, 255, 0.02);
}

.dark-theme .table-controls {
  background-color: rgba(255, 255, 255, 0.03);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 图片样式 */
.enhanced-image-container {
  margin: 2em 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.enhanced-image {
  display: block;
  max-width: 100%;
  height: auto;
  margin: 0 auto;
}

.image-caption {
  padding: 0.5em 0;
  font-size: 0.85em;
  color: rgba(0, 0, 0, 0.6);
  text-align: center;
  background-color: transparent;
  border-top: none;
  /* 确保在关闭图片显示时，标题也被隐藏 */
  display: block;
  font-style: italic;
}

/* 图片控件已移除 */

.background-image-container {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.extracted-background-image {
  max-height: 300px;
  object-fit: contain;
}

/* 暗色主题图片样式 */
.dark-theme .enhanced-image-container {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark-theme .image-caption {
  color: rgba(255, 255, 255, 0.6);
  background-color: transparent;
  border-top: none;
}

/* 暗色主题下的图片控件已移除 */

/* 代码块样式 - 使用内联样式替代 */
/* 这部分样式已经移动到内联样式中 */

/* 列表样式 - 使用内联样式替代 */
/* 这部分样式已经移动到内联样式中 */

/* 视频样式 */
.enhanced-video-container {
  margin: 2em 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enhanced-video {
  display: block;
  width: 100%;
  max-width: 100%;
}

.video-caption {
  padding: 0.75em 1em;
  font-size: 0.9em;
  color: rgba(0, 0, 0, 0.7);
  text-align: center;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.enhanced-video-embed {
  position: relative;
  padding-bottom: 56.25%;
  /* 16:9 比例 */
  height: 0;
  overflow: hidden;
  max-width: 100%;
  margin: 2em 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enhanced-video-embed iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.enhanced-iframe-container {
  width: 100%;
  overflow: hidden;
  margin: 2em 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.iframe-loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 暗色主题视频样式 */
.dark-theme .enhanced-video-container {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark-theme .video-caption {
  color: rgba(255, 255, 255, 0.7);
  background-color: rgba(255, 255, 255, 0.03);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .enhanced-video-embed {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark-theme .enhanced-iframe-container {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark-theme .iframe-loading-indicator {
  background-color: rgba(0, 0, 0, 0.7);
}

.dark-theme .loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-top-color: #60a5fa;
}

/* 响应式调整 */
@media (max-width: 768px) {

  .enhanced-table th,
  .enhanced-table td {
    padding: 0.5em 0.75em;
    font-size: 0.9em;
  }

  .table-controls {
    flex-direction: column;
    gap: 0.5em;
  }

  .table-search-input,
  .table-sort-select {
    width: 100%;
  }

  .enhanced-code-container pre {
    font-size: 0.85em;
  }

  .code-header {
    flex-wrap: wrap;
  }

  .code-caption {
    width: 100%;
    margin-left: 0;
    margin-top: 0.5em;
  }
}