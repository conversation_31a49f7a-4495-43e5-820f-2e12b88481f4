/**
 * Material Design 3 Theme Variables
 * Based on Material You color system
 */

:root {
  /* ===== Light Theme Colors ===== */
  --md-sys-color-primary: #006397;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-primary-container: #c2e7ff;
  --md-sys-color-on-primary-container: #001d36;

  --md-sys-color-secondary: #51565c;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-secondary-container: #d3d8de;
  --md-sys-color-on-secondary-container: #0f1419;

  --md-sys-color-tertiary: #6b5b92;
  --md-sys-color-on-tertiary: #ffffff;
  --md-sys-color-tertiary-container: #f2daff;
  --md-sys-color-on-tertiary-container: #251431;

  --md-sys-color-error: #ba1a1a;
  --md-sys-color-on-error: #ffffff;
  --md-sys-color-error-container: #ffdad6;
  --md-sys-color-on-error-container: #410002;

  --md-sys-color-background: #fdfcff;
  --md-sys-color-on-background: #1a1c1e;

  --md-sys-color-surface: #fdfcff;
  --md-sys-color-on-surface: #1a1c1e;
  --md-sys-color-surface-variant: #dde3ea;
  --md-sys-color-on-surface-variant: #41484d;

  --md-sys-color-surface-dim: #d7d9dd;
  --md-sys-color-surface-bright: #fdfcff;
  --md-sys-color-surface-container-lowest: #ffffff;
  --md-sys-color-surface-container-low: #f1f0f4;
  --md-sys-color-surface-container: #e3e2e6;
  --md-sys-color-surface-container-high: #dddce0;
  --md-sys-color-surface-container-highest: #d8d7da;

  --md-sys-color-outline: #71787e;
  --md-sys-color-outline-variant: #c1c7ce;

  --md-sys-color-inverse-surface: #2f3133;
  --md-sys-color-inverse-on-surface: #f1f0f4;
  --md-sys-color-inverse-primary: #7dcdff;

  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;

  /* ===== Elevation Shadows ===== */
  --md-sys-elevation-level0: none;
  --md-sys-elevation-level1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level4: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level5: 0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);

  /* ===== Typography Scale ===== */
  --md-sys-typescale-display-large-font-family-name: 'Google Sans';
  --md-sys-typescale-display-large-font-family-style: 'Regular';
  --md-sys-typescale-display-large-font-weight: 400;
  --md-sys-typescale-display-large-font-size: 57px;
  --md-sys-typescale-display-large-line-height: 64px;
  --md-sys-typescale-display-large-letter-spacing: -0.25px;

  --md-sys-typescale-display-medium-font-family-name: 'Google Sans';
  --md-sys-typescale-display-medium-font-family-style: 'Regular';
  --md-sys-typescale-display-medium-font-weight: 400;
  --md-sys-typescale-display-medium-font-size: 45px;
  --md-sys-typescale-display-medium-line-height: 52px;
  --md-sys-typescale-display-medium-letter-spacing: 0px;

  --md-sys-typescale-display-small-font-family-name: 'Google Sans';
  --md-sys-typescale-display-small-font-family-style: 'Regular';
  --md-sys-typescale-display-small-font-weight: 400;
  --md-sys-typescale-display-small-font-size: 36px;
  --md-sys-typescale-display-small-line-height: 44px;
  --md-sys-typescale-display-small-letter-spacing: 0px;

  --md-sys-typescale-headline-large-font-family-name: 'Google Sans';
  --md-sys-typescale-headline-large-font-family-style: 'Regular';
  --md-sys-typescale-headline-large-font-weight: 400;
  --md-sys-typescale-headline-large-font-size: 32px;
  --md-sys-typescale-headline-large-line-height: 40px;
  --md-sys-typescale-headline-large-letter-spacing: 0px;

  --md-sys-typescale-headline-medium-font-family-name: 'Google Sans';
  --md-sys-typescale-headline-medium-font-family-style: 'Regular';
  --md-sys-typescale-headline-medium-font-weight: 400;
  --md-sys-typescale-headline-medium-font-size: 28px;
  --md-sys-typescale-headline-medium-line-height: 36px;
  --md-sys-typescale-headline-medium-letter-spacing: 0px;

  --md-sys-typescale-headline-small-font-family-name: 'Google Sans';
  --md-sys-typescale-headline-small-font-family-style: 'Regular';
  --md-sys-typescale-headline-small-font-weight: 400;
  --md-sys-typescale-headline-small-font-size: 24px;
  --md-sys-typescale-headline-small-line-height: 32px;
  --md-sys-typescale-headline-small-letter-spacing: 0px;

  --md-sys-typescale-title-large-font-family-name: 'Roboto';
  --md-sys-typescale-title-large-font-family-style: 'Regular';
  --md-sys-typescale-title-large-font-weight: 400;
  --md-sys-typescale-title-large-font-size: 22px;
  --md-sys-typescale-title-large-line-height: 28px;
  --md-sys-typescale-title-large-letter-spacing: 0px;

  --md-sys-typescale-title-medium-font-family-name: 'Roboto';
  --md-sys-typescale-title-medium-font-family-style: 'Medium';
  --md-sys-typescale-title-medium-font-weight: 500;
  --md-sys-typescale-title-medium-font-size: 16px;
  --md-sys-typescale-title-medium-line-height: 24px;
  --md-sys-typescale-title-medium-letter-spacing: 0.15px;

  --md-sys-typescale-title-small-font-family-name: 'Roboto';
  --md-sys-typescale-title-small-font-family-style: 'Medium';
  --md-sys-typescale-title-small-font-weight: 500;
  --md-sys-typescale-title-small-font-size: 14px;
  --md-sys-typescale-title-small-line-height: 20px;
  --md-sys-typescale-title-small-letter-spacing: 0.1px;

  --md-sys-typescale-label-large-font-family-name: 'Roboto';
  --md-sys-typescale-label-large-font-family-style: 'Medium';
  --md-sys-typescale-label-large-font-weight: 500;
  --md-sys-typescale-label-large-font-size: 14px;
  --md-sys-typescale-label-large-line-height: 20px;
  --md-sys-typescale-label-large-letter-spacing: 0.1px;

  --md-sys-typescale-label-medium-font-family-name: 'Roboto';
  --md-sys-typescale-label-medium-font-family-style: 'Medium';
  --md-sys-typescale-label-medium-font-weight: 500;
  --md-sys-typescale-label-medium-font-size: 12px;
  --md-sys-typescale-label-medium-line-height: 16px;
  --md-sys-typescale-label-medium-letter-spacing: 0.5px;

  --md-sys-typescale-label-small-font-family-name: 'Roboto';
  --md-sys-typescale-label-small-font-family-style: 'Medium';
  --md-sys-typescale-label-small-font-weight: 500;
  --md-sys-typescale-label-small-font-size: 11px;
  --md-sys-typescale-label-small-line-height: 16px;
  --md-sys-typescale-label-small-letter-spacing: 0.5px;

  --md-sys-typescale-body-large-font-family-name: 'Roboto';
  --md-sys-typescale-body-large-font-family-style: 'Regular';
  --md-sys-typescale-body-large-font-weight: 400;
  --md-sys-typescale-body-large-font-size: 16px;
  --md-sys-typescale-body-large-line-height: 24px;
  --md-sys-typescale-body-large-letter-spacing: 0.5px;

  --md-sys-typescale-body-medium-font-family-name: 'Roboto';
  --md-sys-typescale-body-medium-font-family-style: 'Regular';
  --md-sys-typescale-body-medium-font-weight: 400;
  --md-sys-typescale-body-medium-font-size: 14px;
  --md-sys-typescale-body-medium-line-height: 20px;
  --md-sys-typescale-body-medium-letter-spacing: 0.25px;

  --md-sys-typescale-body-small-font-family-name: 'Roboto';
  --md-sys-typescale-body-small-font-family-style: 'Regular';
  --md-sys-typescale-body-small-font-weight: 400;
  --md-sys-typescale-body-small-font-size: 12px;
  --md-sys-typescale-body-small-line-height: 16px;
  --md-sys-typescale-body-small-letter-spacing: 0.4px;

  /* ===== Shape Scale ===== */
  --md-sys-shape-corner-none: 0px;
  --md-sys-shape-corner-extra-small: 4px;
  --md-sys-shape-corner-small: 8px;
  --md-sys-shape-corner-medium: 12px;
  --md-sys-shape-corner-large: 16px;
  --md-sys-shape-corner-extra-large: 28px;
  --md-sys-shape-corner-full: 9999px;

  /* ===== Motion ===== */
  --md-sys-motion-duration-short1: 50ms;
  --md-sys-motion-duration-short2: 100ms;
  --md-sys-motion-duration-short3: 150ms;
  --md-sys-motion-duration-short4: 200ms;
  --md-sys-motion-duration-medium1: 250ms;
  --md-sys-motion-duration-medium2: 300ms;
  --md-sys-motion-duration-medium3: 350ms;
  --md-sys-motion-duration-medium4: 400ms;
  --md-sys-motion-duration-long1: 450ms;
  --md-sys-motion-duration-long2: 500ms;
  --md-sys-motion-duration-long3: 550ms;
  --md-sys-motion-duration-long4: 600ms;

  --md-sys-motion-easing-linear: cubic-bezier(0, 0, 1, 1);
  --md-sys-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-standard-accelerate: cubic-bezier(0.3, 0, 1, 1);
  --md-sys-motion-easing-standard-decelerate: cubic-bezier(0, 0, 0, 1);
  --md-sys-motion-easing-emphasized: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-emphasized-accelerate: cubic-bezier(0.3, 0, 0.8, 0.15);
  --md-sys-motion-easing-emphasized-decelerate: cubic-bezier(0.05, 0.7, 0.1, 1);
}

/* ===== Dark Theme ===== */
.dark {
  --md-sys-color-primary: #7dcdff;
  --md-sys-color-on-primary: #003258;
  --md-sys-color-primary-container: #004a77;
  --md-sys-color-on-primary-container: #c2e7ff;

  --md-sys-color-secondary: #b7bcc2;
  --md-sys-color-on-secondary: #24292e;
  --md-sys-color-secondary-container: #3a3f44;
  --md-sys-color-on-secondary-container: #d3d8de;

  --md-sys-color-tertiary: #d6bbff;
  --md-sys-color-on-tertiary: #3b2948;
  --md-sys-color-tertiary-container: #523f6f;
  --md-sys-color-on-tertiary-container: #f2daff;

  --md-sys-color-error: #ffb4ab;
  --md-sys-color-on-error: #690005;
  --md-sys-color-error-container: #93000a;
  --md-sys-color-on-error-container: #ffdad6;

  --md-sys-color-background: #1a1c1e;
  --md-sys-color-on-background: #e3e2e6;

  --md-sys-color-surface: #1a1c1e;
  --md-sys-color-on-surface: #e3e2e6;
  --md-sys-color-surface-variant: #41484d;
  --md-sys-color-on-surface-variant: #c1c7ce;

  --md-sys-color-surface-dim: #1a1c1e;
  --md-sys-color-surface-bright: #3a3c3e;
  --md-sys-color-surface-container-lowest: #0f1113;
  --md-sys-color-surface-container-low: #1a1c1e;
  --md-sys-color-surface-container: #1e2022;
  --md-sys-color-surface-container-high: #282a2c;
  --md-sys-color-surface-container-highest: #333537;

  --md-sys-color-outline: #8b9297;
  --md-sys-color-outline-variant: #41484d;

  --md-sys-color-inverse-surface: #e3e2e6;
  --md-sys-color-inverse-on-surface: #2f3133;
  --md-sys-color-inverse-primary: #006397;

  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;
}